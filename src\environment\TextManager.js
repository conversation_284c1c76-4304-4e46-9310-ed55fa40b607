import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'

function createTextTexture(text, options = {}) {
    const font = options.font || '12px sans-serif';
    const color = options.color || '#FFFFFF';
    const backgroundColor = options.backgroundColor || 'rgba(0, 0, 0, 0)'; // Transparent background

    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');

    context.font = font;
    const textMetrics = context.measureText(text);

    canvas.width = textMetrics.width;
    canvas.height = 48; // Set a fixed height based on font size

    // Redraw with full settings
    context.font = font;
    context.fillStyle = backgroundColor;
    context.fillRect(0, 0, canvas.width, canvas.height);

    context.fillStyle = color;
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.fillText(text, canvas.width / 2, canvas.height / 2);

    return new THREE.CanvasTexture(canvas);

}
export class TextManager {
    constructor(scene) {
        this.scene = scene;
    }

    createWelcomeText() {
        const welcomeTextTexture = createTextTexture('Welcome to My Portfolio!', {
            font: 'bold 12px "Silkscreen"', // ✅ Use the custom font name
            color: '#333333'
        });

        const textMaterial = new THREE.MeshBasicMaterial({
            map: welcomeTextTexture,
            transparent: true
        });

        const aspectRatio = welcomeTextTexture.image.width / welcomeTextTexture.image.height;
        const textGeometry = new THREE.PlaneGeometry(5 * aspectRatio, 5);
        const textMesh = new THREE.Mesh(textGeometry, textMaterial);

        textMesh.rotation.x = -Math.PI / 2;
        textMesh.position.set(0, 0.01, 10);

        this.scene.add(textMesh);
    }
}