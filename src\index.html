<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            overflow: hidden;
            background: #000;
            font-family: 'Courier New', monospace;
        }
        
        canvas.webgl {
            position: fixed;
            top: 0;
            left: 0;
            outline: none;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }
        
        .controls {
            position: fixed;
            bottom: 20px;
            left: 20px;
            color: white;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 5px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <canvas class="webgl"></canvas>
    <div class="controls">
        <div>WASD: Move character</div>
        <div>HD-2D Portfolio</div>
    </div>
    <script type="module" src="./script.js"></script>
</body>
</html>
