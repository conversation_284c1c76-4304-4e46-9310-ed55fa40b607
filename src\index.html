<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio</title>

    <!-- Load Silkscreen font from Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Silkscreen:wght@400;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        @font-face {
            font-family: 'Silkscreen';
            src: url('/fonts/Silkscreen-Regular.ttf') format('truetype');
        }

        body {
            overflow: hidden;
            background: #000;
            font-family: 'Silkscreen', 'Courier New', monospace;
        }

        /* Ensure Silkscreen font is loaded and available */
        .font-preload {
            font-family: 'Silkscreen';
            position: absolute;
            left: -9999px;
            visibility: hidden;
        }

        /* style.css */
        #container {
            position: relative;
            /* 子要素の位置の基準となる */
            width: 100vw;
            height: 100vh;
        }

        #background-gradient {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            /* 紫からピンクがかった黄色へのグラデーション */
            background: linear-gradient(to bottom, #a881e3, #f5d7b5);
            z-index: -1;
            /* canvasの背面に配置 */
        }

        .webgl {
            /* canvasを背景の上に重ねる */
            position: absolute;
            top: 0;
            left: 0;
            outline: none;
        }

        canvas.webgl {
            position: fixed;
            top: 0;
            left: 0;
            outline: none;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }

        .controls {
            position: fixed;
            bottom: 20px;
            left: 20px;
            color: white;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 5px;
            font-size: 12px;
        }

        #project-link-container {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            padding: 15px 30px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            border-radius: 10px;
            font-size: 1.2em;
            transition: opacity 0.3s, visibility 0.3s;
        }

        #project-link-container.hidden {
            opacity: 0;
            visibility: hidden;
        }

        #project-link-container a {
            color: white;
            text-decoration: none;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <!-- Font preload element to ensure Silkscreen is available -->
    <div class="font-preload">Silkscreen Font Preload</div>

    <div id="container">
        <div id="background-gradient"></div>
        <canvas class="webgl"></canvas>
    </div>
    <div class="controls">
        <div>WASD: Move character</div>
        <div>HD-2D Portfolio</div>
    </div>
    <div id="project-link-container" class="hidden">
        <a href="#" target="_blank" rel="noopener noreferrer"></a>
    </div>
    <script type="module" src="./script.js"></script>
</body>

</html>